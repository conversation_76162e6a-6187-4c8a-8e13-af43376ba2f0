# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
Preprocess the SWE-RL dataset to parquet format
"""

import argparse
import os
import jsonlines
import datasets


def read_jsonl(file_path, data_chunk=0):
    """
    Read a JSONL file and return a list of dictionaries.
    """
    data = []
    # dataset_len = 18500
    # only reading the range(data_chunk * 3083, (data_chunk + 1) * 3083) 



    # dataset_len = 56728
    count = 0
    with jsonlines.open(file_path) as reader:
        for obj in reader:
            if count < data_chunk * 5000:
                count += 1
                continue
            if count >= (data_chunk + 1) * 5000:
                break
            data.append(obj)
            count += 1
    return data



# from verl.utils.hdfs_io import copy, makedirs

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--local_dir", default="../data/swe_rl_train_filter")
    parser.add_argument("--hdfs_dir", default=None)
    parser.add_argument("--data_chunk", type=int, default=0,
                        help="The chunk of data to process, default is 0. ")

    args = parser.parse_args()

    file_path = "../data/swe_rl_train/qwen_rl_sampling/processed/high_quality_filtered_2246.jsonl"

    data_chunk = args.data_chunk
    raw_data = read_jsonl(file_path, data_chunk=data_chunk)

    # issue data_chunk= 1
    # now data_chunk=10 doing, in total 11 chunks
    # remove the bad instance in raw_data that cause the error: pyarrow.lib.ArrowInvalid: cannot mix list and non-list, non-null values


    # Debug: Check data structure to identify inconsistencies and normalize them
    print(f"Total raw_data items: {len(raw_data)}")
    if raw_data:
        print("Sample data keys:", raw_data[0].keys())

        # Check for inconsistent data types across items and normalize them
        inconsistent_items = []
        normalized_count = 0
        for i, item in enumerate(raw_data):
            for key, value in item.items():
                # Check if this field has inconsistent types across items
                if i == 0:
                    continue
                first_item_type = type(raw_data[0][key])
                current_item_type = type(value)
                if first_item_type != current_item_type:
                    print(f"Type inconsistency found at item {i}, key '{key}': {first_item_type} vs {current_item_type}")
                    print(f"First item value: {raw_data[0][key]}")
                    print(f"Current item value: {value}")
                    inconsistent_items.append(i)

                    # Normalize the inconsistent value instead of removing the item
                    try:
                        if first_item_type == list and current_item_type != list:
                            # Convert non-list to list
                            raw_data[i][key] = [value] if value is not None else []
                            normalized_count += 1
                            print(f"Normalized item {i}, key '{key}' from {current_item_type} to list")
                        elif first_item_type != list and current_item_type == list:
                            # Convert list to non-list (take first element or default)
                            raw_data[i][key] = value[0] if value else None
                            normalized_count += 1
                            print(f"Normalized item {i}, key '{key}' from list to {first_item_type}")
                        elif first_item_type == str and current_item_type != str:
                            # Convert to string
                            raw_data[i][key] = str(value) if value is not None else ""
                            normalized_count += 1
                            print(f"Normalized item {i}, key '{key}' from {current_item_type} to string")
                        elif first_item_type in (int, float) and current_item_type in (int, float, str):
                            # Convert to numeric type
                            try:
                                raw_data[i][key] = first_item_type(value)
                                normalized_count += 1
                                print(f"Normalized item {i}, key '{key}' from {current_item_type} to {first_item_type}")
                            except (ValueError, TypeError):
                                # If conversion fails, use default value
                                raw_data[i][key] = 0 if first_item_type in (int, float) else None
                                normalized_count += 1
                                print(f"Normalized item {i}, key '{key}' to default value due to conversion error")
                        else:
                            # For other types, try to convert or use None
                            try:
                                raw_data[i][key] = first_item_type(value)
                                normalized_count += 1
                                print(f"Normalized item {i}, key '{key}' from {current_item_type} to {first_item_type}")
                            except (ValueError, TypeError):
                                raw_data[i][key] = None
                                normalized_count += 1
                                print(f"Normalized item {i}, key '{key}' to None due to conversion error")
                    except Exception as e:
                        print(f"Error normalizing item {i}, key '{key}': {e}")
                        # As a last resort, set to None
                        raw_data[i][key] = None
                        normalized_count += 1
                    break

        # Report normalization results instead of removing items
        if inconsistent_items:
            print(f"Found {len(inconsistent_items)} items with type inconsistencies")
            print(f"Successfully normalized {normalized_count} inconsistent values")
            print(f"All {len(raw_data)} items retained (no data waste)")
        else:
            print("No type inconsistencies found")

    dataset = datasets.Dataset.from_list(raw_data)
    # print(dataset)
    # exit()
    # dataset = datasets.load_dataset(data_source)

    train_dataset = dataset
    # test_dataset = dataset["test"]

    # instruction_following = (
    #     r"You FIRST think about the reasoning process as an internal monologue and then provide the final answer. "
    #     r"The reasoning process MUST BE enclosed within <think> </think> tags. The final answer MUST BE put in \boxed{}."
    # )

    # add a row to each data item that represents a unique id
    def make_map_fn(split):
        def process_fn(example, idx):
            # 'instance_id': instance_id,
            # 'found_files': relevant_file_contents,
            # 'problem_statement': problem_statement,
            # 'prompt': prompt, 
            # 'FAIL_TO_PASS': FAIL_TO_PASS,
            # 'PASS_TO_PASS': PASS_TO_PASS,
            # 'test_patch': test_patch,
            # 'patch': patch


            question = example.pop("prompt")
            relevant_file_contents = example.pop("relevant_file_contents")
            answer_patch = example.pop("patch")

            data = {
                "data_source": "swe_rl",
                "prompt": [
                    {
                        "role": "user",
                        "content": question,
                    }
                ],
                "ability": "swe-agent",
                "reward_model": {"style": "rule", "ground_truth": answer_patch},
                "extra_info": {
                    "split": split,
                    "index": idx,
                    "relevant_file_contents": relevant_file_contents,
                    # "reward_clipping": 0.5
                },
            }
            return data

        return process_fn


    train_dataset = train_dataset.map(function=make_map_fn("train"), with_indices=True, num_proc=8)
    # repeat the train_dataset 100 times
    # train_dataset = train_dataset.select(range(1000))

    # exit()
    local_dir = args.local_dir
    hdfs_dir = args.hdfs_dir

    train_dataset.to_parquet(os.path.join(local_dir, "train_{}.parquet".format(data_chunk)))
   

    # if hdfs_dir is not None:
    #     makedirs(hdfs_dir)
    #     copy(src=local_dir, dst=hdfs_dir)
